#!/usr/bin/env python3
"""
测试E0V1E_ShortOnly策略的基本功能
"""

import os
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), "user_data", "strategies"))

import numpy as np
import pandas as pd
from E0V1E_ShortOnly import E0V1E_ShortOnly


def create_test_dataframe():
    """创建测试数据"""
    # 创建300个数据点的测试数据
    dates = pd.date_range("2024-01-01", periods=300, freq="5min")

    # 模拟价格数据 - 创建一个上涨趋势然后回调的模式
    base_price = 100
    trend = np.linspace(0, 20, 300)  # 上涨趋势
    noise = np.random.normal(0, 2, 300)  # 随机噪声
    prices = base_price + trend + noise

    # 确保价格为正数
    prices = np.maximum(prices, 50)

    # 创建OHLCV数据
    df = pd.DataFrame(
        {
            "date": dates,
            "open": prices * (1 + np.random.normal(0, 0.01, 300)),
            "high": prices * (1 + np.abs(np.random.normal(0, 0.02, 300))),
            "low": prices * (1 - np.abs(np.random.normal(0, 0.02, 300))),
            "close": prices,
            "volume": np.random.uniform(1000, 10000, 300),
        }
    )

    # 确保OHLC关系正确
    df["high"] = df[["open", "high", "low", "close"]].max(axis=1)
    df["low"] = df[["open", "high", "low", "close"]].min(axis=1)

    return df


def test_strategy():
    """测试策略功能"""
    print("开始测试E0V1E_ShortOnly策略...")

    # 创建基本配置
    config = {
        "timeframe": "5m",
        "stake_currency": "USDT",
        "stake_amount": 100,
        "dry_run": True,
        "trading_mode": "futures",
        "margin_mode": "isolated",
    }

    # 创建策略实例
    strategy = E0V1E_ShortOnly(config)

    # 创建测试数据
    df = create_test_dataframe()
    print(f"创建了{len(df)}行测试数据")

    # 测试指标计算
    print("\n1. 测试指标计算...")
    try:
        df_with_indicators = strategy.populate_indicators(df, {"pair": "BTC/USDT"})
        print("✓ 指标计算成功")

        # 检查关键指标是否存在
        required_indicators = [
            "rsi",
            "rsi_fast",
            "rsi_slow",
            "sma_15",
            "cti",
            "atr_pct",
            "bb_percent",
        ]
        missing_indicators = [
            ind for ind in required_indicators if ind not in df_with_indicators.columns
        ]

        if missing_indicators:
            print(f"✗ 缺少指标: {missing_indicators}")
        else:
            print("✓ 所有必需指标都已计算")

    except Exception as e:
        print(f"✗ 指标计算失败: {e}")
        return False

    # 测试入场信号
    print("\n2. 测试入场信号...")
    try:
        df_with_entry = strategy.populate_entry_trend(df_with_indicators, {"pair": "BTC/USDT"})

        # 检查是否有做空信号
        if "enter_short" in df_with_entry.columns:
            short_signals = df_with_entry["enter_short"].sum()
            print(f"✓ 入场信号计算成功，发现{short_signals}个做空信号")
        else:
            print("✗ 没有找到enter_short列")

    except Exception as e:
        print(f"✗ 入场信号计算失败: {e}")
        return False

    # 测试出场信号
    print("\n3. 测试出场信号...")
    try:
        df_with_exit = strategy.populate_exit_trend(df_with_entry, {"pair": "BTC/USDT"})
        print("✓ 出场信号计算成功")

    except Exception as e:
        print(f"✗ 出场信号计算失败: {e}")
        return False

    # 测试参数访问
    print("\n4. 测试策略参数...")
    try:
        print(f"✓ 时间框架: {strategy.timeframe}")
        print(f"✓ 止损: {strategy.stoploss}")
        print(f"✓ 启动蜡烛数: {strategy.startup_candle_count}")
        print(f"✓ 做空RSI阈值: {strategy.short_rsi.value}")
        print(f"✓ 做空SMA比率: {strategy.short_sma_ratio.value}")

    except Exception as e:
        print(f"✗ 参数访问失败: {e}")
        return False

    print("\n✅ 策略测试完成！所有基本功能正常。")
    return True


if __name__ == "__main__":
    success = test_strategy()
    if success:
        print("\n🎉 E0V1E_ShortOnly策略已成功创建并通过基本测试！")
        print("\n策略特点:")
        print("- 纯做空策略，基于超买回调逻辑")
        print("- 支持主信号和备用信号两种入场模式")
        print("- 包含完整的风险管理和动态出场机制")
        print("- 支持动态杠杆和仓位调整")
        print("- 包含市场环境过滤和成交量确认")
        print("\n建议下一步:")
        print("1. 使用freqtrade backtesting进行回测")
        print("2. 根据回测结果调整参数")
        print("3. 在模拟环境中测试")
    else:
        print("\n❌ 策略测试失败，请检查代码。")
        sys.exit(1)
